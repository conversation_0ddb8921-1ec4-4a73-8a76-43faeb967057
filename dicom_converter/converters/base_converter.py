"""
Base converter class for SimpleDicomConverter.

This module provides the base class for all DICOM converters in the application.
"""

import os
import logging
from abc import ABC, abstractmethod
from pydicom.dataset import Dataset, FileDataset, FileMetaDataset
from pydicom.uid import UID
from pinnacle_io.readers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PatientReader, TrialReader

from pinnacle_io.models import ImageSet, Patient, Plan, Trial
from dicom_converter.utils.constants import (
    IMPLEMENTATION_CLASS_UID,
    TRANSFER_SYNTAX_UID,
    MANUFACTURER,
    STATION_NAME,
    SPECIFIC_CHARACTER_SET,
    MODALITY_TO_FILE_PREFIX,
    DICOM_EXTENSION,
)

# Setup logging
logger = logging.getLogger(__name__)


class DicomConverter(ABC):
    """
    Base class for all DICOM converters.

    This abstract class defines the common interface and functionality for
    all DICOM converters in the application. Specific converter classes for
    different DICOM modalities should inherit from this class.
    """

    MODALITY = None

    def __init__(self, patient: Patient):
        """
        Initialize the DICOM converter with a Patient model.

        Args:
            patient: Patient model object.
        """
        self.patient = patient
        self.patient_path = patient.patient_path if patient else ""
        self.patient_folder = os.path.basename(self.patient_path) if self.patient_path else ""
        self.institution_path = os.path.dirname(os.path.dirname(self.patient_path)) if self.patient_path else ""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Initialize common attributes from patient
        self.first_name = patient.first_name if patient else ""
        self.middle_name = patient.middle_name if patient else ""
        self.last_name = patient.last_name if patient else ""
        self.patient_id = patient.medical_record_number if patient else ""
        self.patient_birth_date = patient.date_of_birth if patient else ""
        self.patient_sex = patient.gender if patient else ""
        self.study_date = ""
        self.study_time = ""
        self.study_instance_uid = ""
        self.series_instance_uid = ""
        self.frame_of_reference_uid = ""

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a DICOM converter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            DicomConverter instance initialized with patient data.
        """
        # # Load patient data from the archive
        # patient_reader = PatientFileReader()
        # patient = patient_reader.read(os.path.join(patient_path, "Patient"))

        # # Set the full patient path on the patient model
        # patient.patient_path = patient_path

        # # Return a new instance with the loaded patient
        # return cls(patient)
        return PatientReader.read_from_path(patient_path)

    def create_file_meta(self, sop_class_uid: str, sop_instance_uid: str) -> FileMetaDataset:
        """
        Create a DICOM file meta dataset containing standard metadata required for DICOM files.

        Args:
            sop_class_uid: SOP Class UID for the DICOM file.
            sop_instance_uid: SOP Instance UID for the DICOM file.

        Returns:
            DICOM file meta dataset.
        """
        file_meta = FileMetaDataset()
        file_meta.MediaStorageSOPClassUID = UID(sop_class_uid)
        file_meta.MediaStorageSOPInstanceUID = UID(sop_instance_uid)
        file_meta.TransferSyntaxUID = UID(TRANSFER_SYNTAX_UID)
        file_meta.ImplementationClassUID = UID(IMPLEMENTATION_CLASS_UID)

        return file_meta

    def create_dataset(self, file_meta: FileMetaDataset) -> FileDataset:
        """
        Create a DICOM dataset with the given file meta information and standard attributes.

        Args:
            file_meta: DICOM file meta dataset.

        Returns:
            DICOM file dataset.
        """
        ds = FileDataset("", {}, file_meta=file_meta, preamble=b"\x00" * 128)
        ds.SpecificCharacterSet = SPECIFIC_CHARACTER_SET
        ds.Manufacturer = MANUFACTURER
        ds.StationName = STATION_NAME
        ds.SOPInstanceUID = file_meta.MediaStorageSOPInstanceUID

        return ds

    def set_common_elements(self, ds: FileDataset) -> None:
        """
        Set common DICOM elements in the dataset, such as patient and study information.

        Args:
            ds: DICOM dataset to update.
        """
        import time

        ds.PatientName = "{}^{}^{}".format(self.last_name, self.first_name, self.middle_name)
        ds.PatientID = self.patient_id
        ds.PatientBirthDate = self.patient_birth_date
        ds.PatientSex = self.patient_sex
        ds.StudyDate = self.study_date or time.strftime("%Y%m%d")
        ds.StudyTime = self.study_time or time.strftime("%H%M%S")
        ds.StudyInstanceUID = self.study_instance_uid
        ds.SeriesInstanceUID = self.series_instance_uid
        ds.FrameOfReferenceUID = self.frame_of_reference_uid
        ds.InstanceCreationDate = time.strftime("%Y%m%d")
        ds.InstanceCreationTime = time.strftime("%H%M%S")

    @staticmethod
    def get_dataset_file_name(ds: Dataset) -> str:
        """
        Generate the file name for the DICOM file. Requires the
        DICOM dataset instance Modality and SOP Instance UID attributes to be
        defined.

        Returns:
            The file name in the format: '[prefix].[sop_instance_uid].dcm'.
        """
        if not ds.SOPInstanceUID:
            raise AttributeError("SOP Instance UID not defined. A dataset filename cannot be generated without the SOP Instance UID")

        prefix = MODALITY_TO_FILE_PREFIX.get(ds.Modality, "")
        return f"{prefix}{ds.SOPInstanceUID}{DICOM_EXTENSION}"

    def save_dataset(self, ds: Dataset, output_path: str) -> str:
        """
        Save a DICOM dataset to a file at the specified output path.

        Args:
            ds: DICOM dataset to save. The dataset should have the SOP Instance UID and Modality defined.
            output_path: Path where the dataset will be saved.

        Returns:
            Full path of the output file
        """
        output_file = os.path.join(output_path, self.get_dataset_file_name(ds))

        try:
            # Ensure output directory exists
            os.makedirs(output_path, exist_ok=True)

            ds.save_as(output_file, write_like_original=False)
            self.logger.info(f"Saved DICOM file: {output_file}")
            return output_file
        except Exception as e:
            self.logger.error(f"Error saving DICOM file {output_file}: {e}")
            raise

    @abstractmethod
    def convert(self) -> None:
        """
        Convert Pinnacle files to DICOM.

        This method must be implemented by subclasses to perform the conversion process
        for a specific DICOM modality.
        """
        pass


class PlanBaseConverter(DicomConverter):
    """
    Base class for DICOM converters which depend on a plan (e.g., StructureConverter, DoseConverter).
    """

    def __init__(self, patient: Patient):
        """
        Initialize the Plan base converter.

        Args:
            patient: Patient model object.
        """
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a PlanBaseConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            PlanBaseConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def get_plan(self, plan_path: str) -> Plan | None:
        """
        Retrieve a plan from the patient list by path.

        This method takes the path to a plan folder and returns the corresponding
        Plan model object from the patient's plan list.

        Args:
            plan_path: Full path to the plan folder.

        Returns:
            Plan model object or None if the plan is not found.
        """
        plan_folder = os.path.basename(plan_path)
        plan = next(
            (plan for plan in self.patient.plan_list if f"Plan_{plan.plan_id}" == plan_folder),
            None,
        )
        return plan

    def load_image_set(self, image_set_id: int) -> ImageSet | None:
        """
        Load an image set from the specified image set ID.

        This method takes the ID of an image set and returns the corresponding
        ImageSet model object by reading the image header file from the
        patient's directory.

        Args:
            image_set_id: ID of the image set to load.

        Returns:
            ImageSet model object or None if the image set is not found.
        """
        image_set_path = os.path.join(self.patient_path, f"ImageSet_{image_set_id}.header")
        return ImageSetReader.read(image_set_path)

    def load_planning_ct(self, plan_path: str) -> ImageSet | None:
        """
        Load the planning CT image set for the specified plan.

        This method takes the path to a plan folder and returns the corresponding
        ImageSet model object from the patient's image set list.

        Args:
            plan_path: Full path to the plan folder.

        Returns:
            ImageSet model object or None if the image set is not found.
        """
        plan = self.get_plan(plan_path)
        if plan and plan.primary_ct_image_set_id:
            return self.load_image_set(plan.primary_ct_image_set_id)
        return None


class TrialBaseConverter(PlanBaseConverter):
    """
    Base class for DICOM converters which depend on a trial (e.g., PlanConverter, DoseConverter).
    """

    def __init__(self, patient: Patient):
        """
        Initialize the Trial base converter.

        Args:
            patient: Patient model object.
        """
        super().__init__(patient)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a TrialBaseConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            TrialBaseConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def load_trials(self, plan_path: str) -> list[Trial]:
        """
        Load trials from the specified plan path.

        Args:
            plan_path: Full path to the plan folder containing the trials.

        Returns:
            List of Trial objects loaded from the plan.
        """
        if not os.path.exists(plan_path):
            self.logger.warning(f"Plan path not found: {plan_path}")
            return []
        return TrialReader.read(plan_path)
